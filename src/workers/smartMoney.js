import fs from "fs";
import path from "path";
import { getDb } from "../config/mongodb.js";
import { cache } from "../config/redis.js";
import cron from "node-cron";

const DAYS_TO_FETCH = 15;

const addTokenMetadata = (data, tokenMetadata) => {
  /*
  {
    "_id": "673af143e5ed396b0725271a",
    "chain": "sol",
    "address": "14zP2ToQ79XWvc7FQpm4bRnp9d6Mp1rFfsUW3gpLcRX",
    "ticker": "AIXBT",
    "name": "aixbt by Virtuals (Wormhole)",
    "description": null,
    "decimals": 8,
    "icon": null,
    "links": {},
    "updatedAt": "2024-11-18T07:48:19.985Z",
    "createdAt": "2024-12-14T02:43:24.369Z"
  }
  */
  const metadataMap = new Map(
    tokenMetadata.map((item) => [item.address, item])
  );
  return data.map((item) => {
    const metadata = metadataMap.get(item.token_address);
    return {
      ...item,
      symbol: metadata ? metadata.ticker : null,
      name: metadata ? metadata.name : null,
      decimals: metadata ? metadata.decimals : null,
      logo: metadata ? metadata.icon : null,
      // links: metadata ? (metadata.links || {}) : {},
    };
  });
};

class SmartMoney {
  constructor() {
    this.isRunning = false;
    this.cronJob = null;
  }

  async init() {
    this.isRunning = true;
    console.log("🚀 Initializing SmartMoney worker");

    // Run initial data update
    console.log("📊 Running initial SmartMoney data update...");
    await this.updateData();

    // Start the cron looper
    this.startLooper();

    console.log("✅ SmartMoney worker initialized successfully");
  }

  stop() {
    this.isRunning = false;

    // Stop the cron job if it exists
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
      console.log("🛑 SmartMoney cron job stopped");
    }

    console.log("🛑 SmartMoney worker stopped");
  }

  startLooper() {
    if (this.cronJob) {
      console.log("⚠️ SmartMoney cron job is already running");
      return;
    }

    // Schedule to run every 15 minutes (at 00, 15, 30, 45 minutes of each hour)
    this.cronJob = cron.schedule('0 0,15,30,45 * * * *', async () => {
      if (!this.isRunning) {
        console.log("⚠️ SmartMoney worker is not running, skipping scheduled update");
        return;
      }

      try {
        const startTime = Date.now();
        console.log("⏰ SmartMoney scheduled update started at", new Date().toISOString());

        await this.updateData();

        const duration = Date.now() - startTime;
        console.log(`✅ SmartMoney scheduled update completed in ${duration}ms`);
      } catch (error) {
        console.error("❌ Error in SmartMoney scheduled update:", error);
      }
    }, {
      scheduled: true,
      timezone: "UTC"
    });

    console.log("⏰ SmartMoney cron job started - will run every 15 minutes (00, 15, 30, 45)");
  }

  async updateData() {
    let dbCentral = null;
    let dbJupdca = null;
    try {
      dbCentral = await getDb("centraldata");
      dbJupdca = await getDb("jupdca");
      const tokensNeeded = new Set();
      const excludeCoins = new Set();

      // Get stablecoins and exclude coins
      const [stablecoinResult, solDerivatriveResult] = await Promise.all([
        dbCentral.collection("stablecoins").find().toArray(),
        dbCentral.collection("sol_derivative_tokens").find().toArray(),
      ]);

      stablecoinResult.forEach((coin) => excludeCoins.add(coin.address));
      solDerivatriveResult.forEach((coin) => excludeCoins.add(coin.address));

      // Get date range for queries
      const dateLimit = new Date();
      dateLimit.setDate(dateLimit.getDate() - DAYS_TO_FETCH);

      // Parallel fetch for all required data
      const [most_bought_tokens, most_sold_tokens, dailyFlows] =
        await Promise.all([
          dbJupdca
            .collection("daily_top_token_rankings")
            .aggregate([
              {
                $match: {
                  date: { $gte: dateLimit },
                  rank_type: "top",
                },
              },
              { $sort: { date: -1, rank: 1 } },
              {
                $group: {
                  _id: "$date",
                  tokens: { $push: "$$ROOT" },
                },
              },
              { $project: { tokens: { $slice: ["$tokens", 10] } } },
              { $unwind: "$tokens" },
              { $replaceRoot: { newRoot: "$tokens" } },
            ])
            .sort({ date: -1, rank: 1 })
            .toArray(),
          dbJupdca
            .collection("daily_bottom_token_rankings")
            .aggregate([
              {
                $match: {
                  date: { $gte: dateLimit },
                  rank_type: "bottom",
                },
              },
              { $sort: { date: -1, rank: 1 } },
              {
                $group: {
                  _id: "$date",
                  tokens: { $push: "$$ROOT" },
                },
              },
              { $project: { tokens: { $slice: ["$tokens", 10] } } },
              { $unwind: "$tokens" },
              { $replaceRoot: { newRoot: "$tokens" } },
            ])
            .sort({ date: -1, rank: 1 })
            .toArray(),
          dbJupdca
            .collection("daily_flows")
            .find({ date: { $gte: dateLimit } })
            .sort({ date: -1 })
            .toArray(),
        ]);

      // Collect token addresses for metadata lookup
      most_bought_tokens.forEach((token) =>
        tokensNeeded.add(token.token_address)
      );
      most_sold_tokens.forEach((token) =>
        tokensNeeded.add(token.token_address)
      );

      // get the token metadata for these tokens
      const tokenMetadata = await dbCentral
        .collection("token_metadata")
        .find({ address: { $in: Array.from(tokensNeeded) } })
        .toArray();

      console.log(`📊 SmartMoney: Fetched metadata for ${tokenMetadata.length} tokens`);

      // Add token metadata to the arrays (function returns new arrays with metadata)
      const most_bought_tokens_with_metadata = addTokenMetadata(most_bought_tokens, tokenMetadata);
      const most_sold_tokens_with_metadata = addTokenMetadata(most_sold_tokens, tokenMetadata);

      console.log(`📊 SmartMoney: Added metadata to ${most_bought_tokens_with_metadata.length} bought tokens and ${most_sold_tokens_with_metadata.length} sold tokens`);

      // store in redis use TTL 1 hour = 3600
      await Promise.all([
        cache.set(
          "smart_money:daily_trends:most_bought_tokens",
          most_bought_tokens_with_metadata.map((item) => {
            // remove the _id field
            delete item._id;
            return item;
          }),
          3600
        ),
        cache.set(
          "smart_money:daily_trends:most_sold_tokens",
          most_sold_tokens_with_metadata.map((item) => {
            // remove the _id field
            delete item._id;
            return item;
          }),
          3600
        ),
        cache.set(
          "smart_money:daily_trends:daily_flows_sol",
          dailyFlows.map((item) => {
            return {
              date: item.date,
              buy_volume: item.solFlow.buy_volume,
              sell_volume: item.solFlow.sell_volume,
              net_volume: item.solFlow.net_volume,
            };
          }),
          3600
        ),
        cache.set(
          "smart_money:daily_trends:daily_flows_meme",
          dailyFlows.map((item) => {
            return {
              date: item.date,
              buy_volume: item.memecoinFlow.buy_volume,
              sell_volume: item.memecoinFlow.sell_volume,
              net_volume: item.memecoinFlow.net_volume,
            };
          }),
          3600
        ),
      ]);

      return true;
    } catch (error) {
      console.error("Error in getTrendsCharts:", error);
      throw error;
    } finally {
      // Clean up variables
      dbCentral = null;
      dbJupdca = null;
    }
  }
}

export default SmartMoney;
