import { cache } from '../src/config/redis.js';

async function testSmartMoneyEndpoint() {
    try {
        console.log('🧪 Testing Smart Money endpoint...');
        
        // First, let's populate some test data
        const testData = [
            {
                date: "2025-06-06T00:00:00.000Z",
                token_address: "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
                buy_volume: 173186.12129621796,
                sell_volume: 56803.705254317436,
                net_volume: 116382.41604190052,
                rank_type: "top",
                rank: 1,
                symbol: "Fartcoin",
                name: "Fartcoin",
                decimals: 6,
                logo: "https://ipfs.io/ipfs/QmQr3Fz4h1etNsF7oLGMRHiCzhB5y9a7GjyodnF7zLHK1g"
            },
            {
                date: "2025-06-06T00:00:00.000Z",
                token_address: "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
                buy_volume: 6798.710464928154,
                sell_volume: 25306.60396926343,
                net_volume: -18507.893504335276,
                rank_type: "top",
                rank: 2,
                symbol: "Bonk",
                name: "Bonk",
                decimals: 5,
                logo: "https://arweave.net/hQiPZOsRZXGXBJd_82PhVdlM_hACsT_q6wqwf5cSY7I"
            }
        ];
        
        console.log('📝 Setting test data in Redis...');
        const setResult = await cache.set('smart_money:daily_trends:most_bought_tokens', testData, 3600);
        
        if (setResult) {
            console.log('✅ Test data set successfully');
        } else {
            console.log('❌ Failed to set test data');
            process.exit(1);
        }
        
        // Now test retrieval
        console.log('🔍 Testing data retrieval...');
        const retrievedData = await cache.get('smart_money:daily_trends:most_bought_tokens');
        
        if (retrievedData && Array.isArray(retrievedData) && retrievedData.length > 0) {
            console.log(`✅ Successfully retrieved ${retrievedData.length} items`);
            console.log('📊 Sample data:');
            console.log(JSON.stringify(retrievedData[0], null, 2));
        } else {
            console.log('❌ Failed to retrieve data or data is empty');
        }
        
        // Test the endpoint with curl
        console.log('\n🌐 Testing the actual endpoint...');
        console.log('You can test the endpoint with:');
        console.log('curl -H "X-API-Key: demo_api_key_12345" http://localhost:3001/api/v1/smart-money/daily-trends/most-bought-tokens');
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    }
}

testSmartMoneyEndpoint();
