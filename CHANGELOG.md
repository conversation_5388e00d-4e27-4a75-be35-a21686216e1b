# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **Smart Money Daily Trends - Most Bought Tokens Endpoint** - New endpoint `/api/v1/smart-money/daily-trends/most-bought-tokens`
  - Available to Basic, Premium, and Enterprise tiers (Free tier excluded)
  - Costs 2 credits per request
  - Returns most bought tokens by smart money traders over the last 15 days
  - Includes token metadata, volume data, and ranking information
  - Data sourced from Redis cache populated by SmartMoney worker

### Fixed
- **Rate Limiter Hanging Issues** - Resolved timeout issues with rate limiting middleware
  - Identified Redis store implementation was causing request hanging
  - Temporarily reverted to memory store for rate limiting to ensure reliability
  - Smart Money endpoint now responds correctly without timeouts
  - Added TODO for future Redis store implementation improvement

### Database
- **Smart Money Endpoint Access Configuration** - Added new endpoint to tier permissions
  - Updated `access_tiers` table to include `/api/v1/smart-money/daily-trends/most-bought-tokens`
  - Added endpoint to Basic, Premium, and Enterprise tiers
  - Created migration script `SQL/10_add_smart_money_endpoints.sql`
- **SmartMoney Worker Cron Scheduler** - Implemented automated data collection for smart money trading insights
  - **Cron Schedule**: Runs every 15 minutes (at 00, 15, 30, 45 minutes of each hour) using UTC timezone
  - **Data Sources**: Aggregates data from Jupiter DCA orders, token rankings, and daily flow data
  - **Redis Caching**: Stores processed data in Redis with 1-hour TTL for optimal performance
    - `smart_money:most_bought_tokens` - Top 10 bought tokens per day (last 15 days)
    - `smart_money:most_sold_tokens` - Top 10 sold tokens per day (last 15 days)
    - `smart_money:daily_flows_sol` - Daily SOL flow data (buy/sell/net volumes)
    - `smart_money:daily_flows_meme` - Daily memecoin flow data (buy/sell/net volumes)
  - **Robust Error Handling**: Comprehensive error handling with detailed logging and graceful failure recovery
  - **Lifecycle Management**: Proper initialization, cron job management, and graceful shutdown integration
  - **Token Metadata Enrichment**: Fetches and processes token symbols and metadata for enhanced data quality
  - **Automatic Startup**: Runs initial data update on application startup, then follows scheduled updates
  - **Resource Management**: Proper database connection cleanup and memory management
  - **Development Support**: Saves test data to `temp/smartmoney_test.json` for debugging and development
  - **Comprehensive Logging**: Detailed logging for monitoring cron execution, performance, and error tracking
  - **Documentation**: Created `docs/SMART_MONEY_WORKER_GUIDE.md` with complete usage and configuration guide
  - **Result**: Automated smart money data collection providing up-to-date trading insights every 15 minutes

### Fixed
- **SmartMoney Worker Cron Implementation** - Fixed incorrect cron job configuration and method binding
  - **Cron Schedule Fix**: Changed from `'* * * * * *'` (every second) to `'0 0,15,30,45 * * * *'` (every 15 minutes)
  - **Method Binding Fix**: Corrected `this.updateData().bind(this)` to proper arrow function `() => this.updateData()`
  - **Job Reference Storage**: Added `this.cronJob` property to store cron job reference for proper lifecycle management
  - **Automatic Startup**: Added `startLooper()` call to `init()` method for automatic cron job initialization
  - **Graceful Shutdown**: Enhanced `stop()` method to properly stop cron job and prevent resource leaks
  - **Unused Variable Cleanup**: Removed unused `result` variable and improved `tokenMetadata` usage with logging
  - **Error Handling**: Added try-catch wrapper around cron job execution with detailed error logging
  - **Execution Tracking**: Added execution time tracking and performance logging for monitoring
  - **Worker State Checking**: Added `isRunning` flag validation to prevent execution when worker is stopped
  - **Token Metadata Integration**: Fixed `addTokenMetadata` function to properly assign returned arrays with metadata
    - **Function Return Issue**: Fixed incorrect function call that wasn't assigning returned metadata-enriched arrays
    - **Variable Assignment**: Changed from `addTokenMetadata(array, metadata)` to `array_with_metadata = addTokenMetadata(array, metadata)`
    - **Redis Cache Update**: Updated cache operations to use metadata-enriched arrays for storage
    - **Test File Update**: Updated test file output to include metadata-enriched data
    - **Metadata Fields**: Each token now includes `symbol`, `name`, `decimals`, and `logo` fields from token metadata
    - **Test Validation**: Updated test suite to verify metadata fields are properly included in cached data
  - **Result**: SmartMoney worker now runs reliably every 15 minutes with proper error handling, lifecycle management, and token metadata enrichment
- **MongoDB Integration** - Complete MongoDB NoSQL database integration for flexible document storage
  - **Configuration**: Added MongoDB connection configuration in `src/config/mongodb.js`
    - Connection pooling with optimized settings (5-20 connections)
    - Automatic reconnection and error handling
    - Connection event handlers for monitoring
    - Graceful shutdown integration
  - **Helper Functions**: Comprehensive MongoDB helper functions for common operations
    - `insertOne()`, `insertMany()` - Document insertion
    - `findOne()`, `find()` - Document retrieval with query support
    - `updateOne()`, `updateMany()` - Document updates with filters
    - `deleteOne()`, `deleteMany()` - Document deletion
    - `countDocuments()` - Document counting
    - `createIndex()` - Index management
    - `aggregate()` - Aggregation pipeline support
    - `getCollection()` - Direct collection access
  - **Application Integration**: MongoDB initialized alongside PostgreSQL and Redis
    - Added to health check endpoint (`/health`) showing MongoDB status
    - Integrated into service initialization process
    - Graceful shutdown handling for MongoDB connections
    - Connection testing during startup
  - **Environment Configuration**: Added MongoDB URI configuration
    - Updated `.env.example` with MongoDB connection string
    - Support for both local MongoDB and MongoDB Atlas cloud connections
  - **Testing**: Created comprehensive test suite (`test/mongodb-test.js`)
    - Tests all CRUD operations
    - Verifies connection functionality
    - Validates helper function behavior
  - **Documentation**: Complete MongoDB integration guide
    - Created `docs/MONGODB_GUIDE.md` with configuration, usage, and best practices
    - Updated main documentation to include MongoDB in technical stack
    - Added MongoDB guide to documentation index
  - **Use Cases**: Optimized for analytics data, logging, and user preferences storage
    - Flexible schema for analytics events
    - Application logging with structured data
    - User settings and preferences storage
  - **Result**: API now supports both relational (PostgreSQL) and document (MongoDB) data storage patterns

### Fixed
- **Database Connection Cleanup Error** - Fixed "Called end on pool more than once" error during graceful shutdown
  - **Root Cause**: PostgreSQL pool was being closed multiple times during shutdown process
  - **Solution**: Enhanced pool closure detection using pool state instead of module-level flags
  - **Database Pool Protection**: Check `pool.ended` property to prevent multiple closure attempts
  - **Redis Connection Protection**: Added `redisClosed` flag to prevent multiple Redis disconnections
  - **MongoDB Connection Protection**: Added `mongoClosed` flag to prevent multiple MongoDB disconnections
  - **Import Optimization**: Changed from dynamic imports to static imports for better module state management
  - **Error Handling**: Enhanced error handling for connection closure failures with specific error message detection
  - **Result**: Clean graceful shutdown without connection pool errors, exit code 0
- **Database Connection Cleanup Error** - Fixed "Called end on pool more than once" error during graceful shutdown
  - **Root Cause**: PostgreSQL pool was being closed multiple times during shutdown process
  - **Solution**: Added safeguards to prevent multiple closure attempts for all database connections
  - **Database Pool Protection**: Added `poolClosed` flag to track PostgreSQL pool state
  - **Redis Connection Protection**: Added `redisClosed` flag to prevent multiple Redis disconnections
  - **MongoDB Connection Protection**: Added `mongoClosed` flag to prevent multiple MongoDB disconnections
  - **Error Handling**: Enhanced error handling for connection closure failures
  - **Result**: Clean graceful shutdown without connection pool errors
- **CRITICAL**: Fixed database connection pool exhaustion after WebSocket disconnections
  - Added immediate client removal from subscriptions to prevent broadcast attempts to disconnected clients
  - Implemented timeout protection for credit checking operations (3-second timeout)
  - Added fallback mechanism when credit checks fail to prevent message delivery failures
  - Improved error handling in credit consumption with individual error catching
  - Made database operations during disconnection non-blocking with timeout protection
  - Enhanced cleanup process to be more immediate and prevent race conditions

### Added
- **Environment Variables**: Added missing configuration variables to `.env`
  - `DB_MIN_CONNECTIONS=10`: Minimum database connections in pool
  - `DB_MAX_CONNECTIONS=100`: Maximum database connections in pool (increased from 20)
  - `WS_MAX_GLOBAL_CONNECTIONS=10000`: Global WebSocket connection limit
  - `WS_MAX_USER_CONNECTIONS=10`: Per-user WebSocket connection limit
  - `DEBUG_DATABASE=false`: Control database query logging
  - `DEBUG_WEBSOCKET=false`: Control WebSocket message logging
  - `DEBUG_SOLANA_TRACKER=false`: Control SolanaTracker debug logging

### Changed
- **Content Security Policy**: Updated CSP to allow Redoc documentation to function properly
  - Added `'unsafe-eval'` and `blob:` to `script-src` for web worker support
  - Added `https://cdn.jsdelivr.net` to `script-src` for CDN access
  - Added explicit `worker-src` directive for web worker control
- **Comprehensive Performance & Security Analysis** - Conducted thorough codebase analysis identifying critical bottlenecks and vulnerabilities
  - **Performance Analysis**: Identified 5 critical performance bottlenecks affecting scalability:
    - Database connection pool limitations (20 connections max causing bottlenecks)
    - Redis single instance architecture (no clustering/failover)
    - Inefficient rate limiting using memory store instead of Redis
    - Missing database query optimizations and caching
    - Unbounded WebSocket connection growth without limits
  - **Security Analysis**: Identified 5 critical security vulnerabilities:
    - API key exposure in development logs
    - Insufficient input validation across endpoints
    - Weak security headers configuration (CSP disabled)
    - Insufficient rate limiting with bypass vulnerabilities
    - Weak authentication security (no API key expiration)
  - **Resource Management Issues**: Identified memory leaks and unbounded data storage
  - **Created Documentation**: `docs/PERFORMANCE_SECURITY_ANALYSIS.md` with detailed findings and recommendations
  - **Implementation Guide**: `docs/CRITICAL_FIXES_IMPLEMENTATION.md` with step-by-step fixes for critical issues
  - **Priority Matrix**: Categorized issues by impact/effort with clear implementation timeline
  - **Scalability Targets**: Defined current limitations and recommended targets for production scale

- **Critical Performance & Security Fixes Implementation** - Implemented immediate high-priority fixes for production readiness
  - **Database Connection Pool Optimization**: Increased from 20 to 100 connections with monitoring
    - Added connection pool event handlers for monitoring
    - Implemented connection rotation (7500 uses) and validation
    - Added minimum connections (10) and optimized timeouts
    - Enhanced error handling to prevent process crashes in production
  - **Redis-backed Rate Limiting**: Replaced memory store with persistent Redis storage
    - Implemented Redis store for express-rate-limit middleware
    - Added proper key prefixing and TTL management
    - Enabled rate limit persistence across server restarts
    - Added batch operations for better performance
  - **Enhanced Security Headers**: Fixed helmet configuration for production security
    - Enabled Content Security Policy with proper directives
    - Added HSTS with preload for production environments
    - Implemented comprehensive security headers (XSS, CSRF, etc.)
    - Configured CSP to allow necessary resources while blocking attacks
  - **Comprehensive Input Validation**: Added robust validation middleware
    - Created `src/middleware/validation.js` with sanitization functions
    - Implemented schema-based validation for all endpoints
    - Added password strength validation and email format checking
    - Included XSS prevention through input sanitization
  - **WebSocket Connection Limits**: Implemented global and per-user connection limits
    - Added configurable limits (10,000 global, 10 per user)
    - Implemented automatic stale connection cleanup (5-minute intervals)
    - Added graceful shutdown with proper connection cleanup
    - Enhanced connection tracking and resource management
  - **Environment Configuration**: Updated with new performance and security settings
    - Added database pool configuration variables
    - Included WebSocket connection limit settings
    - Added debug flags for controlled verbose logging
  - **Testing Framework**: Created comprehensive test suite for critical fixes
    - `tests/test-critical-fixes.js` validates all implemented fixes
    - Tests database pool performance, Redis rate limiting, input validation
    - Verifies WebSocket connection limits and security configurations
    - Added `pnpm test:critical-fixes` command for validation

- **Rate Limiting & Tier Synchronization Fixes** - Critical fixes for rate limiting accuracy and cache consistency
  - **Fixed Rate Limiting Tier Sync**: Rate limits now properly reflect current database tier configurations
    - Added `getFreshUserTierLimits()` function to fetch current limits when cache is stale
    - Enhanced rate limiter to check fresh database limits when user data seems cached
    - Rate limiting now shows accurate current limits in error messages
  - **Enhanced Cache Invalidation**: Comprehensive cache clearing when tier configurations change
    - Fixed `clearTierCaches()` function to properly clear all related caches
    - Added rate limiting cache invalidation when user tiers are updated
    - Implemented batch cache deletion for better performance
    - Added detailed logging for cache clearing operations
  - **User Tier Management**: Added admin endpoint for changing user tiers with proper cache invalidation
    - New `PUT /admin/users/:userId/tier` endpoint for tier changes
    - Automatic cache clearing when user tier is updated via User model
    - Comprehensive validation and error handling for tier changes
    - Detailed response with cache clearing confirmation
  - **Authentication Enhancement**: Marked fresh database lookups for rate limiting accuracy
    - Added `_freshLookup` flag to distinguish cached vs fresh user data
    - Enhanced authentication middleware to support rate limiting synchronization
  - **Comprehensive Testing**: Created test suite for tier synchronization validation
    - `tests/test-tier-rate-limit-sync.js` validates tier-rate limit synchronization
    - Tests cache invalidation, fresh limit retrieval, and admin tier changes
    - Added `pnpm test:tier-sync` command for validation
    - Verifies rate limits update immediately when tiers change
- **Comprehensive Codebase Cleanup and Optimization** - Performed extensive code quality improvements across the entire live-api codebase
  - **Console.log Statement Cleanup**: Removed or conditioned debug console.log statements that would clutter production logs
    - `src/workers/kolFeed.js`: Removed verbose success logging, kept essential error logging
    - `src/workers/kafkaStream.js`: Removed commented debug logs
    - `src/workers/solanaTracker.js`: Made debug logging conditional on `DEBUG_SOLANA_TRACKER=true` environment variable
    - `src/websocket/WebSocketServer.js`: Made WebSocket message logging conditional on `DEBUG_WEBSOCKET=true` environment variable
    - `src/websocket/StreamManager.js`: Removed commented debug logs from Redis subscriptions
    - `src/config/database.js`: Made query logging conditional on `DEBUG_DATABASE=true` environment variable
  - **Comprehensive Code Documentation**: Added detailed functional comments to all major files
    - `src/websocket/WebSocketServer.js`: Added class and method documentation for WebSocket server functionality
    - `src/websocket/StreamManager.js`: Added comprehensive documentation for stream management logic
    - `src/workers/kolFeed.js`: Added detailed comments for KOL feed data processing and transformation
    - `src/workers/kafkaStream.js`: Added documentation for Kafka message processing and data transformation
    - `src/workers/solanaTracker.js`: Fixed unused parameter warnings in transformation functions
  - **Code Optimization**: Improved error handling patterns and async/await usage
    - Standardized error logging across all workers
    - Optimized conditional logging to reduce production noise
    - Improved code readability with meaningful comments
    - Enhanced debugging capabilities with environment-controlled verbose logging
  - **Unused Code Removal**: Cleaned up commented code and optimized imports
    - Removed commented console.log statements throughout the codebase
    - Updated TODO comments to be more descriptive and actionable
    - Fixed unused parameter warnings in transformation functions
  - **Environment Variable Enhancements**: Added new debug flags for controlled verbose logging
    - `DEBUG_SOLANA_TRACKER=true`: Enable detailed SolanaTracker message logging
    - `DEBUG_WEBSOCKET=true`: Enable WebSocket message logging
    - `DEBUG_DATABASE=true`: Enable database query logging
  - **Result**: Cleaner, more maintainable codebase with improved debugging capabilities and reduced production log noise

### Changed
- **Removed token-metadata Stream** - Completely removed the token-metadata WebSocket stream from the codebase
  - **StreamManager.js**: Removed token-metadata stream registration and room mapping logic
  - **SolanaTracker Worker**: Removed transformMetadataData() function and metadata case handling
  - **Redoc Documentation**: Removed token-metadata stream documentation and examples
  - **Result**: Cleaner codebase with 9 Premium streams instead of 10, focusing on actively used functionality

- **Snake_Case Conversion for Enterprise Streams** - Implemented automatic camelCase to snake_case conversion for consistent API formatting
  - **Created Case Converter Utility**: New `src/utils/caseConverter.js` with recursive key conversion functions
    - `convertKeysToSnakeCase()`: Converts camelCase keys to snake_case recursively through objects and arrays
    - Smart detection: Only converts actual camelCase keys (starts with lowercase, contains uppercase)
    - Preserves token addresses and other non-camelCase strings
  - **Updated Kafka Stream Worker**: Modified `src/workers/kafkaStream.js` to apply snake_case conversion
    - Jupiter AMM swaps: `inputMint` → `input_mint`, `outputAmount` → `output_amount`, `childRoutes` → `child_routes`, `priceInverse` → `price_inverse`
    - Pumpfun AMM swaps: `isAmm` → `is_amm`, `fishyTransaction` → `fishy_transaction`, `eventData` → `event_data`
    - All nested objects and arrays are converted recursively
  - **Updated Documentation**: Corrected Redoc examples to show snake_case field names
  - **Updated Example Files**: Modified temp examples to reflect new snake_case format
  - **Result**: All Enterprise streams now use consistent snake_case formatting matching the rest of the API

- **100% Accurate Redoc Documentation Update** - Comprehensive review and correction of WebSocket streams documentation to match actual implementation
  - **Implementation-Based Accuracy**: Analyzed actual source code (`StreamManager.js`, `solanaTracker.js`, temp examples) to ensure 100% documentation accuracy
  - **Corrected Parameter Names**: Fixed parameter requirements to match actual implementation:
    - `pool-changes`: Uses `pool_id` parameter (not `pool`)
    - `price-updates`: Uses `token` parameter (not `pool`)
    - `tokens-graduating`: Added optional `market_cap` parameter for threshold filtering
  - **Accurate Response Structures**: Updated all example responses to reflect actual data transformation functions:
    - **Premium Streams**: Real field names from `solanaTracker.js` transformations (symbol, mint, decimals, has_metadata_file, risk.score, pools array with pool_id, market_cap, liquidity, etc.)
    - **Enterprise Streams**: Exact response structures from temp examples (jupiter-amm-swaps, pumpfun-amm-swaps, jupiter-dca-orders)
    - **Transaction Streams**: Array format with proper field names (transaction_time, wallet, program, type, token_in/token_out objects)
  - **Verified Tier Requirements**: Confirmed actual tier requirements from database schema and stream definitions
  - **Correct Credit Costs**: Validated credit costs from actual implementation (kol-feed: 2 credits, Premium streams: 0 credits, Enterprise streams: 0 credits)
  - **Real Data Examples**: Used actual field structures and realistic values from working implementation
  - **Parameter Format Validation**: Ensured subscription formats match exactly what StreamManager expects
  - **Complete Stream Coverage**: All 14 streams now have accurate specifications matching the codebase
  - **Result**: Documentation now provides 100% accurate information that developers can rely on for successful WebSocket integration

### Fixed
- **SolanaTracker Stream Subscription Issue** - Fixed critical bug where WebSocket subscriptions to SolanaTracker streams (tokens-launched, tokens-graduating, etc.) were not triggering the actual SolanaTracker worker connections
  - Added automatic stream subscription handler in StreamManager that listens to Redis `stream_subscription` events
  - When users subscribe to SolanaTracker streams via WebSocket, the system now automatically subscribes the worker to the corresponding SolanaTracker room
  - Maps stream names to SolanaTracker rooms: `tokens-launched` → `latest`, `tokens-graduating` → `graduating`, etc.
  - Handles both subscription and unsubscription events for proper connection lifecycle management
  - Added missing environment variables `SOLANA_TRACKER_WSS_URL` and `SOLANA_TRACKER_WSS_KEY` to `.env.example`
  - **Result**: Enterprise users can now successfully receive real-time token launch data from SolanaTracker API

- **SolanaTracker Stream Cleanup Resource Leak** - Fixed critical issue where SolanaTracker WebSocket connections remained open after users disconnected, causing resource leaks
  - **Enhanced Bulk Unsubscription**: Implemented efficient bulk cleanup when users disconnect from WebSocket, preventing individual unsubscription calls for each stream
  - **Improved Connection Cleanup**: Enhanced connection removal with proper WebSocket event listener cleanup to prevent memory leaks
  - **Graceful Connection Closure**: Added graceful WebSocket closure with fallback to forced termination for robust cleanup
  - **Disconnect Cleanup Tracking**: Implemented tracking system to prevent duplicate cleanup attempts for the same connection
  - **Immediate Cleanup**: Fixed connection pool to immediately close connections when no subscribers remain (removed 500ms delay)
  - **Reconnection Cleanup**: Clear pending reconnection attempts when connections are properly closed
  - **Parameter Mapping Fix**: Corrected `price-updates` stream to use `pool_id` parameter instead of `token` for proper SolanaTracker room mapping
  - **Comprehensive Error Handling**: Added robust error handling for connection cleanup failures with detailed logging
  - **Test Coverage**: Created `tests/test_solana_tracker_cleanup.js` to verify cleanup functionality

### Added
- **Comprehensive SolanaTracker WebSocket Test Suite** - Created extensive testing framework to verify all 10 SolanaTracker streams are working correctly
  - Tests all stream types: tokens-launched, tokens-graduating, tokens-graduated, pool-changes, token-transactions, price-updates, wallet-transactions, token-metadata, token-holders, token-changes
  - Implements intelligent timeout strategies based on expected message frequency (30s-5min)
  - Verifies complete data flow: SolanaTracker API → StreamManager → WebSocket clients
  - Provides detailed debugging and comprehensive reporting for each stream
  - Validates subscription confirmation and real-time data delivery

- **Complete SolanaTracker WebSocket Documentation** - Created comprehensive documentation for all SolanaTracker stream subscriptions
  - Updated `docs/SOLANA_TRACKER_GUIDE.md` with verified subscription formats and real data examples
  - Created `docs/SOLANA_TRACKER_WEBSOCKET_REFERENCE.md` as quick reference for all 10 streams
  - Documented correct subscription format: `{"type": "subscribe", "payload": {"stream": "stream-name"}}`
  - Clarified that colon-separated parameters (e.g., "tokens-graduating:sol:100") are NOT supported at WebSocket level
  - Included real data examples from live testing (AINTERN, dolf, GoMint, Cat On Mask, The Rock tokens)
  - Added stream frequency expectations, error handling, and troubleshooting guides
  - Updated WebSocket guide with SolanaTracker subscription examples

- **Parameterized SolanaTracker Graduating Streams** - Added support for market cap threshold parameters in tokens-graduating stream
  - Implemented parameter support in WebSocket subscription payload: `{"stream": "tokens-graduating", "parameters": {"marketCapThreshold": 175}}`
  - Maps to SolanaTracker room format: `graduating:sol:175` for specific market cap thresholds
  - Updated StreamManager to handle parameterized room mapping
  - Enhanced SolanaTracker worker to support parameterized graduating rooms
  - Updated data transformation to include market cap threshold information in responses
  - Added comprehensive test coverage for parameterized graduating streams
  - Updated all documentation with parameterized subscription examples

- **Critical Memory Leak Fix** - Implemented comprehensive cleanup system to prevent SolanaTracker connection memory leaks
  - **Automatic Unsubscription on Disconnect**: WebSocket disconnections now automatically publish unsubscription events to Redis
  - **SolanaTracker Connection Cleanup**: StreamManager properly handles unsubscription events to clean up SolanaTracker connections
  - **Orphaned Connection Detection**: Added periodic maintenance to detect and clean up connections with no subscribers
  - **Maintenance Scheduler**: Implemented 5-minute periodic cleanup to prevent memory accumulation
  - **Graceful Shutdown Enhancement**: Improved shutdown process to properly clean up all active connections
  - **Database Session Tracking**: Enhanced session management to track disconnections and cleanup
  - **Comprehensive Testing**: Created test suite to verify automatic cleanup on client disconnect scenarios

### Added
- **SolanaTracker Worker Module** - New efficient proxy for SolanaTracker WebSocket API
  - Implemented connection pooling for WebSocket streams to minimize API usage
  - Created modular data transformation functions for easy maintenance and refactoring
  - Added support for all SolanaTracker room types (latest, graduating, graduated, pool, transaction, price, wallet, metadata, holders, token)
  - Integrated with existing StreamManager system following established patterns
  - Added proper error handling, reconnection logic with exponential backoff, and logging
  - Implemented transaction deduplication to prevent duplicate processing
  - **Premium+ tier feature with 0 credit cost** - Included as part of Premium and Enterprise subscriptions
  - **Real-time only** - No historical data storage to optimize performance and reduce complexity
  - Added comprehensive documentation in `docs/SOLANA_TRACKER_GUIDE.md`

### Technical Implementation
- **Connection Pooling System**:
  - Multiple users can subscribe to the same SolanaTracker endpoint without creating duplicate connections
  - Connections are automatically created when needed and closed when no subscribers remain
  - Efficient resource management with subscriber tracking per room
  
- **Modular Architecture**:
  - Discrete transformation functions for each data type (latest, transactions, price updates, etc.)
  - Abstracted data source details for future flexibility
  - Well-named functions that can be easily refactored
  
- **Integration Features**:
  - Follows KolFeed and KafkaStreams worker patterns
  - Uses Redis pub/sub for internal communication
  - Stores historical data in Redis lists (last 100 messages per stream)
  - Integrates with StreamManager's event-driven system
  - Supports dynamic subscription/unsubscription

### Environment Variables
- Added `SOLANA_TRACKER_WSS_URL` - WebSocket URL for SolanaTracker API (wss://datastream.solanatracker.io)
- Added `SOLANA_TRACKER_WSS_KEY` - API key for SolanaTracker authentication

### New Streams Available (Premium+ Tier, 0 Credits)
**Basic Streams:**
- `tokens-launched` - Latest tokens and pools from Solana blockchain
- `tokens-graduating` - Tokens approaching graduation on Pump.fun/Moonshot
- `tokens-graduated` - Recently graduated tokens

**Parameterized Streams:**
- `pool-changes` - Real-time updates for specific pools
- `token-transactions` - Transaction updates for specific tokens
- `price-updates` - Price updates for specific pools/tokens (supports multiple formats)
- `wallet-transactions` - Transaction updates for specific wallets
- `token-metadata` - Token metadata updates (BETA)
- `token-holders` - Token holder count changes (BETA)
- `token-changes` - All updates for specific tokens

### Database Updates
- ✅ **COMPLETED**: Added SolanaTracker stream definitions to `stream_definitions` table
- ✅ **UPDATED**: Changed tier requirement to "premium" (available for Premium and Enterprise tiers only)
- ✅ **CONFIGURED**: Updated `access_tiers` table with proper stream access permissions
- Set credit cost to 0 for all SolanaTracker streams (included feature)
- **10/10 streams successfully added** to production database

### Access Control & Security
- ✅ **IMPLEMENTED**: Comprehensive tier-based access control system
- ✅ **TESTED**: Basic tier users properly restricted from SolanaTracker streams
- ✅ **VERIFIED**: Premium and Enterprise users have full access to all streams
- ✅ **VALIDATED**: Credit system correctly applies 0 credits for Premium+ users

### Documentation Updates
- ✅ **ENHANCED**: Comprehensive Redoc API documentation with detailed SolanaTracker stream information
- ✅ **ADDED**: Complete WebSocket streaming documentation with examples and troubleshooting
- ✅ **DETAILED**: Parameter requirements and formats for all parameterized streams
- ✅ **IMPROVED**: User-friendly explanations and practical usage examples
- Created comprehensive `docs/SOLANA_TRACKER_GUIDE.md` with:
  - Architecture overview and data flow diagrams
  - Configuration instructions and environment variables
  - Usage examples and API integration guide
  - Data transformation examples
  - Troubleshooting and performance considerations
- Updated `docs/README.md` to include SolanaTracker worker information
- Added SolanaTracker streams to WebSocket streams documentation

### Live Testing Results
- **✅ Connection Success**: WebSocket URL format `WSS_URL/API_KEY` working perfectly
- **✅ Data Structure Validation**: 11+ real messages captured, structure confirmed compatible
- **✅ Connection Pooling Verified**: Multiple subscribers (5) sharing single connection
- **✅ Partial Unsubscription**: Connection maintained with remaining subscribers
- **✅ Complete Unsubscription**: Proper connection cleanup when all users leave
- **✅ Multiple Rooms**: Support for latest, graduating, graduated rooms confirmed
- **✅ Real-time Data**: Successfully receiving live token data from Pump.fun, Meteora, Raydium

### Files Modified
- `src/workers/solanaTracker.js` - New worker module (700+ lines), removed historical storage
- `src/websocket/StreamManager.js` - Integrated SolanaTracker worker, updated stream mappings
- `src/docs/openapi.json` - Added comprehensive SolanaTracker documentation and schemas
- `tests/test_solana_tracker_live.js` - Comprehensive live testing suite
- `tests/test_connection_pooling.js` - Connection pooling validation tests
- `tests/test_solana_tracker_access_control.js` - Comprehensive access control testing
- `tests/debug_solana_tracker_connection.js` - Connection debugging utility
- `scripts/update_solana_tracker_tiers.js` - Database tier configuration script
- `scripts/check_and_add_streams.js` - Database stream addition script
- `database/add_solana_tracker_streams.sql` - SQL statements for database stream definitions
- `docs/SOLANA_TRACKER_GUIDE.md` - New comprehensive documentation
- `docs/README.md` - Updated with SolanaTracker information
- `CHANGELOG.md` - This changelog

### Code Quality
- Follows established patterns from existing workers
- Comprehensive error handling and logging
- Modular design for easy maintenance
- Connection pooling for efficient resource usage
- Automatic cleanup and maintenance routines

---

## Previous Versions

*Previous changelog entries would be documented here as the project evolves*
